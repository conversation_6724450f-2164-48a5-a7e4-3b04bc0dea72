"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  const cardCount = processSteps.length;
  const segmentSize = 1 / cardCount;

  // Handle completion state - when scrollProgress is 1.0, show the last card fully
  let currentCardIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last card at final position
    currentCardIndex = cardCount - 1;
    segmentProgress = 1.0;
  } else {
    currentCardIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentCardIndex = Math.min(currentCardIndex, cardCount - 1);
  }

  // Calculate card frame opacity and position (similar to project cards)
  let cardFrameOpacity = 0;
  let cardFrameTranslateY = 50; // Start from below

  if (scrollProgress > 0) {
    // Card frame slides up and fades in at the beginning
    const frameAnimationProgress = Math.min(1, scrollProgress * 4); // Quick entrance
    cardFrameOpacity = frameAnimationProgress;
    cardFrameTranslateY = 50 * (1 - frameAnimationProgress);
  }

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Current card:', currentCardIndex);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Single card frame that slides up once */}
      <motion.div
        className="relative w-[280px] h-[320px] rounded-2xl shadow-lg overflow-hidden"
        style={{
          opacity: cardFrameOpacity,
          transform: `translateY(${cardFrameTranslateY}px)`,
        }}
        transition={{
          duration: 0.6,
          ease: "easeOut"
        }}
      >
        {/* Render all process steps inside the single frame */}
        {processSteps.map((step, index) => {
          let contentOpacity = 0;
          let maskProgress = 0;
          let blurAmount = 0;

          // Clean, simple logic for each step
          const segmentStart = index * segmentSize;
          const segmentEnd = (index + 1) * segmentSize;

          // Default state
          contentOpacity = 0;
          maskProgress = 0;
          blurAmount = 0;

          // Check if this step should be visible
          if (scrollProgress >= segmentStart + (segmentSize * 0.3)) {
            // Step becomes visible at 30% of its segment
            contentOpacity = 1;

            // For first step (index 0), no mask needed
            if (index === 0) {
              maskProgress = 1;
              blurAmount = 0; // No blur for first step
            }
            // For other steps, handle mask transition starting from when step becomes visible
            else {
              const maskStart = segmentStart + (segmentSize * 0.3); // Start earlier at 30%
              const maskEnd = segmentStart + (segmentSize * 0.8);   // End at 80%

              if (scrollProgress >= maskStart && scrollProgress <= maskEnd) {
                // During mask transition - starts from 0 when step first appears
                const progress = (scrollProgress - maskStart) / (maskEnd - maskStart);
                maskProgress = Math.min(1, Math.max(0, progress));

                // Blur effect: starts strong (8px) and reduces to 0 as mask reveals
                blurAmount = 8 * (1 - progress); // 8px blur at start, 0px at end
              } else if (scrollProgress > maskEnd) {
                // After mask transition completes
                maskProgress = 1;
                blurAmount = 0; // No blur when fully revealed
              } else {
                // Before mask starts (shouldn't happen since contentOpacity check, but safety)
                maskProgress = 0;
                blurAmount = 8; // Full blur before reveal starts
              }
            }
          }

          // Handle when step should disappear (only for non-last steps)
          if (index < cardCount - 1) {
            const nextSegmentStart = segmentEnd;
            const disappearPoint = nextSegmentStart + (segmentSize * 0.8); // Disappear when next step's mask completes

            if (scrollProgress > disappearPoint) {
              contentOpacity = 0;
              maskProgress = 0;
            }
          }

          // Handle completion state for last step
          if (scrollProgress >= 1.0 && index === cardCount - 1) {
            contentOpacity = 1;
            maskProgress = 1;
          }

          return (
            <div
              key={step.id}
              className="absolute inset-0 w-full h-full"
              style={{
                opacity: contentOpacity,
                // Mask reveals from bottom to top (vertical version of project cards)
                clipPath: `inset(${(1 - maskProgress) * 100}% 0 0 0)`,
                // Blur effect during mask transition
                filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
              }}
            >
              {/* Card Content */}
              <div className="w-full h-full bg-primary border-2 border-white/20 rounded-2xl p-6 flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          );
        })}
      </motion.div>
    </div>
  );
};

export default ProcessCard;
